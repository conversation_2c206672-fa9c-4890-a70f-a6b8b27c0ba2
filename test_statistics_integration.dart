import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/core/services/firebase_service.dart';
import 'lib/services/optimized_statistics_service.dart';

/// Simple test to verify Cloud Functions integration
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    print('🚀 Testing OptimizedStatisticsService integration...');
    
    // Initialize Firebase
    await Firebase.initializeApp();
    print('✅ Firebase initialized');
    
    // Initialize Firebase Service
    await FirebaseService.initialize();
    print('✅ FirebaseService initialized');
    
    // Test OptimizedStatisticsService
    final statsService = OptimizedStatisticsService.instance;
    print('✅ OptimizedStatisticsService instance created');
    
    // Test getAggregatedStatistics
    print('📊 Testing getAggregatedStatistics...');
    final stats = await statsService.getAggregatedStatistics();
    print('✅ Statistics received: ${stats.keys.join(', ')}');
    
    // Print some stats
    print('📈 Total Files: ${stats['totalFiles'] ?? 'N/A'}');
    print('📈 Active Users: ${stats['activeUsers'] ?? 'N/A'}');
    print('📈 Total Categories: ${stats['totalCategories'] ?? 'N/A'}');
    print('📈 Recent Files: ${stats['recentFiles'] ?? 'N/A'}');
    
    // Test cache functionality
    print('🔄 Testing cache functionality...');
    final cachedStats = await statsService.getAggregatedStatistics();
    print('✅ Cached statistics retrieved');
    
    // Test force refresh
    print('🔄 Testing force refresh...');
    final refreshedStats = await statsService.getAggregatedStatistics(forceRefresh: true);
    print('✅ Force refresh completed');
    
    // Test pagination
    print('📄 Testing pagination...');
    final paginatedStats = await statsService.getPaginatedFileStats(
      page: 1,
      limit: 10,
    );
    print('✅ Paginated stats received: ${paginatedStats.files.length} files');
    print('📊 Total files in pagination: ${paginatedStats.pagination.total}');
    
    // Test cache invalidation
    print('🗑️ Testing cache invalidation...');
    await statsService.invalidateCache(reason: 'Integration test');
    print('✅ Cache invalidated');
    
    print('🎉 All tests passed! OptimizedStatisticsService is working correctly.');
    
  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('Stack trace: $stackTrace');
    
    // Print specific error details
    if (e.toString().contains('cloud_functions')) {
      print('💡 Suggestion: Make sure Cloud Functions are deployed and accessible');
      print('💡 Run: firebase deploy --only functions');
    }
    
    if (e.toString().contains('permission')) {
      print('💡 Suggestion: Check Firebase authentication and permissions');
    }
    
    if (e.toString().contains('network')) {
      print('💡 Suggestion: Check internet connection and Firebase configuration');
    }
  }
}
